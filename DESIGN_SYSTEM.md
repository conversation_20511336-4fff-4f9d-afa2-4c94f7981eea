# ByeVape Design System

## Overview
The ByeVape design system provides a comprehensive set of design tokens, components, and utilities for building a consistent and accessible vaping cessation support app.

## Color Palette

### Primary Colors
- **Primary**: `#3b2d56` - Deep purple for main branding and primary actions
- **Secondary**: `#6e588b` - Medium purple for secondary elements and accents
- **Accent**: `#a89fc9` - Light purple for highlights and interactive elements
- **Light**: `#f3eefc` - Very light purple for backgrounds
- **Dark**: `#181224` - Dark purple for text and contrast

### Semantic Colors
- **Success**: `#28a745` - Green for positive feedback
- **Warning**: `#ffc107` - Yellow for warnings
- **Error**: `#dc3545` - Red for errors and alerts
- **Info**: `#17a2b8` - Blue for informational messages

## Typography

### Font Families
- **Headings**: `'Playfair Display', serif` - Elegant serif for headlines (Semibold Italic)
- **Body**: `'Source Sans 3', sans-serif` - Clean sans-serif for body text

### Font Sizes
- `--font-size-xs`: 0.75rem (12px)
- `--font-size-sm`: 0.875rem (14px)
- `--font-size-base`: 1rem (16px)
- `--font-size-lg`: 1.125rem (18px)
- `--font-size-xl`: 1.25rem (20px)
- `--font-size-2xl`: 1.5rem (24px)
- `--font-size-3xl`: 1.875rem (30px)
- `--font-size-4xl`: 2.25rem (36px)
- `--font-size-5xl`: 3rem (48px)
- `--font-size-6xl`: 3.75rem (60px)

### Font Weights
- `--font-weight-light`: 300
- `--font-weight-normal`: 400
- `--font-weight-medium`: 500
- `--font-weight-semibold`: 600
- `--font-weight-bold`: 700

## Spacing Scale
- `--spacing-xs`: 0.25rem (4px)
- `--spacing-sm`: 0.5rem (8px)
- `--spacing-md`: 1rem (16px)
- `--spacing-lg`: 1.5rem (24px)
- `--spacing-xl`: 2rem (32px)
- `--spacing-2xl`: 3rem (48px)
- `--spacing-3xl`: 4rem (64px)
- `--spacing-4xl`: 6rem (96px)

## Border Radius
- `--border-radius-sm`: 0.25rem (4px)
- `--border-radius-md`: 0.5rem (8px)
- `--border-radius-lg`: 1rem (16px)
- `--border-radius-xl`: 1.5rem (24px)
- `--border-radius-full`: 9999px

## Shadows
- `--shadow-sm`: Subtle shadow for small elements
- `--shadow-md`: Standard shadow for cards and buttons
- `--shadow-lg`: Prominent shadow for elevated elements
- `--shadow-xl`: Strong shadow for modals and overlays

## Components

### Buttons
- `.btn` - Base button class
- `.btn-primary` - Primary action button (purple)
- `.btn-secondary` - Secondary action button
- `.btn-accent` - Accent button for highlights
- `.btn-outline` - Outline button for secondary actions
- `.btn-lg` - Large button (56px height)
- `.btn-sm` - Small button (40px height)
- `.btn-block` - Full-width button

### Cards
- `.card` - Base card container
- `.card-header` - Card header section
- `.card-title` - Card title styling
- `.card-body` - Main card content
- `.card-footer` - Card footer section

### Progress Bars
- `.progress` - Progress bar container
- `.progress-bar` - Progress bar fill
- `.circular-progress` - Circular progress component

### Forms
- `.form-group` - Form field container
- `.form-label` - Form field label
- `.form-control` - Form input styling

### Alerts
- `.alert` - Base alert component
- `.alert-success` - Success alert
- `.alert-warning` - Warning alert
- `.alert-error` - Error alert
- `.alert-info` - Info alert

## Layout System

### Container
- `.container` - Responsive container with max-widths
- `.app-layout` - Main app layout wrapper
- `.main-content` - Main content area
- `.page` - Individual page wrapper

### Responsive Breakpoints
- Small: 576px
- Medium: 768px
- Large: 992px
- Extra Large: 1200px
- 2X Large: 1400px

## Utility Classes

### Display
- `.d-none`, `.d-block`, `.d-flex`, `.d-inline-flex`, `.d-grid`

### Flexbox
- `.flex-column`, `.flex-row`
- `.justify-content-center`, `.justify-content-between`, `.justify-content-around`
- `.align-items-center`, `.align-items-start`, `.align-items-end`
- `.flex-1`

### Text Alignment
- `.text-center`, `.text-left`, `.text-right`

### Colors
- Text: `.text-primary`, `.text-secondary`, `.text-accent`, etc.
- Background: `.bg-primary`, `.bg-secondary`, `.bg-accent`, etc.

### Spacing
- Margin: `.m-0` to `.m-4`, `.mt-0` to `.mt-4`, etc.
- Padding: `.p-0` to `.p-4`, `.pt-0` to `.pt-4`, etc.

### Border Radius
- `.rounded-sm`, `.rounded`, `.rounded-lg`, `.rounded-xl`, `.rounded-full`

### Shadows
- `.shadow-sm`, `.shadow`, `.shadow-lg`, `.shadow-xl`

## Accessibility Features

### Focus Management
- Custom focus styles with 2px outline
- High contrast mode support
- Reduced motion support for users who prefer it

### Touch Targets
- Minimum 48px touch targets on mobile
- Increased spacing for better usability

### Color Contrast
- All color combinations meet WCAG AA standards
- High contrast mode support

## Mobile-First Design

### Responsive Typography
- Larger font sizes on mobile for better readability
- Adjusted spacing for touch interfaces

### Touch-Friendly Components
- Minimum 48px button heights
- Adequate spacing between interactive elements
- Optimized circular progress size for mobile

## Usage Examples

### Basic Button
```html
<button class="btn btn-primary">Take Vaping Break</button>
```

### Card Component
```html
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Statistics</h3>
  </div>
  <div class="card-body">
    <p>Your progress content here</p>
  </div>
</div>
```

### Circular Progress
```html
<div class="circular-progress">
  <svg>
    <circle class="progress-ring" cx="100" cy="100" r="90"></circle>
    <circle class="progress-ring-fill" cx="100" cy="100" r="90"></circle>
  </svg>
  <div class="progress-text">
    <div class="progress-time">02:45:30</div>
    <div class="progress-label">Next Break</div>
  </div>
</div>
```

## Implementation Notes

1. All CSS custom properties are defined in `:root` for global access
2. Google Fonts are imported for Playfair Display and Source Sans 3
3. Mobile-first responsive design approach
4. Portrait orientation optimized for mobile app usage
5. Accessibility features built-in from the start
6. Dark mode support prepared for future implementation
