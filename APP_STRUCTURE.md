# ByeVape App Structure & Navigation

## Overview
The ByeVape app is built as a single-page application (SPA) with client-side routing and a bottom navigation system optimized for mobile use.

## App Architecture

### Main Components
1. **App Layout** (`app-layout`) - Main container for the entire application
2. **Main Content** (`main-content`) - Scrollable content area for all pages
3. **Bottom Navigation** (`bottom-navigation`) - Fixed navigation bar at the bottom

### Page Structure
The app consists of four main pages:

#### 1. Onboarding Page (`onboarding-page`)
- **Purpose**: First-time user setup and configuration
- **Visibility**: Only shown on first launch or when no user data exists
- **Features**:
  - Step 1: Daily vaping count input
  - Step 2: Cessation speed selection (Fast/Normal/Relaxed)
  - Progressive form with validation
  - Completion triggers navigation to tracker

#### 2. Tracker Page (`tracker-page`) - Main Page
- **Purpose**: Primary interface for daily vaping break tracking
- **Features**:
  - Countdown timer with circular progress indicator
  - Breaks remaining counter with progress bar
  - "Take Vaping Break" primary action button
  - "Early Break" secondary action button
  - Daily summary card with statistics
  - Day counter showing progress in journey

#### 3. Statistics Page (`statistics-page`)
- **Purpose**: Historical data and progress visualization
- **Features**:
  - Weekly overview with averages and improvements
  - Daily breakdown with progress bars
  - Achievement system with success alerts
  - Trend analysis and milestone tracking

#### 4. Settings Page (`settings-page`)
- **Purpose**: App configuration and data management
- **Features**:
  - Current plan display
  - Settings modification (daily breaks, cessation speed)
  - Data management (export/import/reset)
  - App information and version details

## Navigation System

### Bottom Navigation Bar
- **Position**: Fixed at bottom of screen
- **Height**: 70px
- **Items**: 3 navigation items (Tracker, Statistics, Settings)
- **Icons**: Emoji-based icons for universal recognition
- **Active State**: Visual indication of current page
- **Responsive**: Adapts to different screen sizes

### Navigation Items
1. **Tracker** (📊) - Main tracking interface
2. **Statistics** (📈) - Progress and analytics
3. **Settings** (⚙️) - Configuration and management

### Navigation Behavior
- **First Launch**: Navigation hidden during onboarding
- **Page Transitions**: Smooth fade-in animations
- **Active States**: Visual feedback for current page
- **Touch Targets**: Minimum 48px for accessibility

## Routing System

### Client-Side Routing
The app uses a custom JavaScript routing system without external dependencies:

```javascript
class AppState {
  navigateToPage(page) {
    this.currentPage = page;
    this.showPage(page);
    this.updateNavigation();
  }
}
```

### Page Management
- **Show/Hide Logic**: CSS display property manipulation
- **State Persistence**: Current page stored in app state
- **Animation**: CSS transitions for smooth page changes

## Responsive Design

### Mobile-First Approach
- **Primary Target**: Mobile devices in portrait orientation
- **Breakpoints**: 
  - Mobile: < 768px
  - Tablet: 768px - 992px
  - Desktop: > 992px

### Portrait Orientation Lock
- **Implementation**: CSS media queries detect landscape mode
- **Behavior**: Shows rotation message on mobile landscape
- **Purpose**: Ensures optimal user experience

### Touch-Friendly Design
- **Button Sizes**: Minimum 48px touch targets
- **Spacing**: Adequate gaps between interactive elements
- **Typography**: Larger fonts on mobile for readability

## State Management

### Local Storage Integration
- **User Data**: Persistent storage of user preferences and progress
- **First Launch**: Detection and onboarding flow control
- **Daily Reset**: Automatic reset of daily counters

### App State Class
```javascript
class AppState {
  constructor() {
    this.currentPage = 'tracker';
    this.isFirstLaunch = true;
    this.userData = { /* user data structure */ };
  }
}
```

### Data Structure
```javascript
userData = {
  dailyVapingCount: 0,      // Original daily count
  cessationSpeed: 'normal',  // fast/normal/relaxed
  startDate: null,          // Journey start date
  currentDay: 1,            // Days since start
  breaksRemaining: 0,       // Today's remaining breaks
  totalBreaks: 0,           // Today's total allowed
  breaksTaken: 0            // Today's breaks taken
}
```

## Key Features

### Timer System
- **Countdown Timer**: Shows time until next allowed break
- **Circular Progress**: Visual representation of time remaining
- **Auto-Reset**: Daily reset of counters and timers

### Progressive Reduction
- **Algorithm**: Gradual reduction of daily breaks over time
- **Speed Options**: 30/60/90 day programs
- **Automatic**: Applied during daily reset

### Data Persistence
- **Local Storage**: All user data stored locally
- **Export/Import**: JSON-based data portability
- **Reset Option**: Complete app reset functionality

## Accessibility Features

### Keyboard Navigation
- **Focus Management**: Proper tab order and focus indicators
- **Keyboard Support**: All interactive elements accessible via keyboard

### Screen Reader Support
- **Semantic HTML**: Proper heading structure and landmarks
- **ARIA Labels**: Descriptive labels for complex components
- **Alt Text**: Meaningful descriptions for visual elements

### Visual Accessibility
- **Color Contrast**: WCAG AA compliant color combinations
- **Font Sizes**: Scalable typography system
- **Touch Targets**: Minimum 48px for motor accessibility

## Performance Considerations

### Lightweight Architecture
- **No Framework Dependencies**: Vanilla JavaScript for minimal overhead
- **CSS Custom Properties**: Efficient styling system
- **Optimized Assets**: Compressed and minified build output

### Build Output
- **HTML**: ~15KB (compressed: ~3KB)
- **CSS**: ~26KB (compressed: ~6KB)
- **JavaScript**: ~14KB (compressed: ~4KB)
- **Total**: ~55KB (compressed: ~13KB)

## Future Enhancements

### Planned Features
1. **Push Notifications**: Break reminders and encouragement
2. **Data Visualization**: Charts and graphs for statistics
3. **Social Features**: Progress sharing and community support
4. **Offline Support**: Service worker for offline functionality
5. **Dark Mode**: Theme switching capability

### Technical Improvements
1. **Service Worker**: Offline caching and background sync
2. **Web Components**: Modular component architecture
3. **Progressive Web App**: Enhanced PWA features
4. **Performance Monitoring**: Analytics and error tracking

## Development Guidelines

### Code Organization
- **Separation of Concerns**: HTML structure, CSS styling, JS behavior
- **Modular Design**: Reusable components and utilities
- **Consistent Naming**: BEM-like CSS classes and semantic IDs

### Testing Strategy
- **Manual Testing**: Cross-device and cross-browser testing
- **User Testing**: Real user feedback and usability testing
- **Performance Testing**: Load time and interaction responsiveness

### Deployment Process
1. **Build**: `npm run build` - Creates optimized production assets
2. **Sync**: `npx cap sync` - Updates native projects
3. **Test**: Verify functionality across target devices
4. **Deploy**: Android Studio build for distribution
